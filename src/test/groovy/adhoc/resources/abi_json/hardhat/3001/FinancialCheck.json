{"address": "0x017D9Bfe924B7ef9b847078EBcC7934D3e7fA784", "abi": [{"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "checkExchange", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "fromZoneId"}, {"type": "uint16", "name": "toZoneId"}, {"type": "uint256", "name": "amount"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkFinAccountStatus", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bytes32", "name": "accountStatus"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkSyncAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes", "name": "accountSignature"}, {"type": "bytes", "name": "info"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkTransaction", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "bytes", "name": "accountSignature"}, {"type": "bytes", "name": "info"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountLimit", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "accountLimitData", "components": [{"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getBizZoneAccountStatus", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "zoneId"}], "outputs": [{"type": "bytes32", "name": "accountStatus"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0xf2357509fe96c2790371cc60fe7540825434662272986a52d24f6de2dd8b7962", "receipt": {"to": null, "from": "0x027CaDD533C40e8017de7ac814baE35f8b08f402", "contractAddress": "0x017D9Bfe924B7ef9b847078EBcC7934D3e7fA784", "transactionIndex": 0, "gasUsed": "3079118", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xd0db7fe42cac2d83c28ae33a7aa3b1f5799cdced4f6e11065e8bcc37f8319b3d", "blockNumber": 354, "cumulativeGasUsed": "3079118", "status": 1}}