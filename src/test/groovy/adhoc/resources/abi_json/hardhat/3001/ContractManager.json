{"address": "0x99879325E9AB57Af935680ed85d027673165eAA0", "abi": [{"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "accessCtrl", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "account", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "balanceSyncBridge", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "businessZoneAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "financialCheck", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "financialZoneAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "ibcApp", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "string", "name": "ibcAppName"}], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "ibcToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [], "outputs": []}, {"type": "function", "name": "issuer", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "provider", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "setContracts", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "contracts", "components": [{"type": "address", "name": "ctrlAddress"}, {"type": "address", "name": "providerAddress"}, {"type": "address", "name": "issuer<PERSON><PERSON><PERSON>"}, {"type": "address", "name": "validator<PERSON><PERSON><PERSON>"}, {"type": "address", "name": "accountAddress"}, {"type": "address", "name": "financialZoneAccountAddress"}, {"type": "address", "name": "businessZoneAccountAddress"}, {"type": "address", "name": "tokenAddress"}, {"type": "address", "name": "ibc<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "address", "name": "financialCheckAddress"}, {"type": "address", "name": "transferProxyAddress"}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setIbcApp", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "ibcAppAddress"}, {"type": "string", "name": "ibcAppName"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "token", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "transferProxy", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "validator", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x8cc11e352dafc51e0d7a5d0bd13360dd4175ddd809bf611252e8943af539b348", "receipt": {"to": null, "from": "0x81B1425696634636B8d90141452f9491C92797E7", "contractAddress": "0x99879325E9AB57Af935680ed85d027673165eAA0", "transactionIndex": 0, "gasUsed": "1388509", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x00cb87b7c9083ee00d0dd55bfe97298c330cbec84b485c5014679f0c5cb3ea44", "blockNumber": 595, "cumulativeGasUsed": "1388509", "status": 1}}